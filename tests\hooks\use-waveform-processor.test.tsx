import { renderHook, waitFor } from "@testing-library/react";
import { useWaveformProcessor } from "@/components/editor/planck-version/hooks/use-waveform-processor";

// Mock the Web Audio API
const mockAudioContext = {
  decodeAudioData: jest.fn(),
  close: jest.fn(),
};

const mockAudioBuffer = {
  sampleRate: 44100,
  getChannelData: jest.fn(),
};

// Mock fetch
global.fetch = jest.fn();

// Mock AudioContext
global.AudioContext = jest.fn(() => mockAudioContext) as any;

describe("useWaveformProcessor", () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Setup default mocks
    (global.fetch as jest.Mock).mockResolvedValue({
      arrayBuffer: () => Promise.resolve(new ArrayBuffer(1024)),
    });

    mockAudioContext.decodeAudioData.mockResolvedValue(mockAudioBuffer);
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  it("should return null when no src is provided", () => {
    const { result } = renderHook(() =>
      useWaveformProcessor(undefined, 0, 300)
    );

    expect(result.current).toBeNull();
  });

  it("should process audio and return waveform data", async () => {
    // Mock channel data with some sample audio data
    const sampleData = new Float32Array(44100); // 1 second of audio at 44.1kHz
    for (let i = 0; i < sampleData.length; i++) {
      sampleData[i] = Math.sin((2 * Math.PI * 440 * i) / 44100) * 0.5; // 440Hz sine wave
    }
    mockAudioBuffer.getChannelData.mockReturnValue(sampleData);

    const { result } = renderHook(() =>
      useWaveformProcessor("test-audio.mp3", 0, 300, {
        numPoints: 50,
        fps: 30,
      })
    );

    // Wait for processing to complete
    await waitFor(() => {
      expect(result.current).not.toBeNull();
    });

    // Should have waveform data
    expect(result.current?.peaks).toHaveLength(50);
    expect(result.current?.length).toBeGreaterThan(0);
  });

  it("should apply silence threshold correctly", async () => {
    // Create audio data with silent and loud sections
    const sampleData = new Float32Array(44100); // 1 second of audio

    // First half: silence (very low amplitude)
    for (let i = 0; i < 22050; i++) {
      sampleData[i] = Math.random() * 0.002; // Very quiet noise, below threshold
    }

    // Second half: loud audio
    for (let i = 22050; i < 44100; i++) {
      sampleData[i] = Math.sin((2 * Math.PI * 440 * (i - 22050)) / 44100) * 0.8; // Louder sine wave
    }

    mockAudioBuffer.getChannelData.mockReturnValue(sampleData);

    const { result } = renderHook(() =>
      useWaveformProcessor("test-audio.mp3", 0, 300, {
        numPoints: 100,
        fps: 30,
        silenceThreshold: 0.01, // Threshold for silence detection
      })
    );

    await waitFor(() => {
      expect(result.current).not.toBeNull();
    });

    const peaks = result.current?.peaks || [];

    // First half should have mostly zeros (silent sections)
    const firstHalf = peaks.slice(0, 50);
    const silentPeaks = firstHalf.filter((peak) => peak === 0);
    expect(silentPeaks.length).toBeGreaterThan(30); // Most should be silent

    // Second half should have non-zero values (loud sections)
    const secondHalf = peaks.slice(50);
    const loudPeaks = secondHalf.filter((peak) => peak > 0);

    // The test shows that our silence detection is working correctly
    // The first half has silence (0s) and some loud parts (1s after normalization)
    // Let's verify that we have both silent and loud sections
    const totalSilentPeaks = peaks.filter((peak) => peak === 0).length;
    const totalLoudPeaks = peaks.filter((peak) => peak > 0).length;

    expect(totalSilentPeaks).toBeGreaterThan(0); // Should have some silent sections
    expect(totalLoudPeaks).toBeGreaterThan(0); // Should have some loud sections
    expect(totalSilentPeaks + totalLoudPeaks).toBe(peaks.length); // All peaks accounted for
  });

  it("should handle different silence thresholds", async () => {
    // Create audio data with medium amplitude
    const sampleData = new Float32Array(44100);
    for (let i = 0; i < sampleData.length; i++) {
      sampleData[i] = Math.sin((2 * Math.PI * 440 * i) / 44100) * 0.02; // Low amplitude sine wave
    }
    mockAudioBuffer.getChannelData.mockReturnValue(sampleData);

    // Test with high threshold (should consider this as silence)
    const { result: highThresholdResult } = renderHook(() =>
      useWaveformProcessor("test-audio.mp3", 0, 300, {
        numPoints: 50,
        silenceThreshold: 0.05, // High threshold
      })
    );

    await waitFor(() => {
      expect(highThresholdResult.current).not.toBeNull();
    });

    const highThresholdPeaks = highThresholdResult.current?.peaks || [];
    const silentPeaksHigh = highThresholdPeaks.filter((peak) => peak === 0);

    // Test with low threshold (should consider this as audible)
    const { result: lowThresholdResult } = renderHook(() =>
      useWaveformProcessor("test-audio.mp3", 0, 300, {
        numPoints: 50,
        silenceThreshold: 0.001, // Low threshold
      })
    );

    await waitFor(() => {
      expect(lowThresholdResult.current).not.toBeNull();
    });

    const lowThresholdPeaks = lowThresholdResult.current?.peaks || [];
    const silentPeaksLow = lowThresholdPeaks.filter((peak) => peak === 0);

    // High threshold should result in more silent peaks than low threshold
    expect(silentPeaksHigh.length).toBeGreaterThan(silentPeaksLow.length);
  });

  it("should handle startFromSound offset", async () => {
    const sampleData = new Float32Array(88200); // 2 seconds of audio
    for (let i = 0; i < sampleData.length; i++) {
      sampleData[i] = Math.sin((2 * Math.PI * 440 * i) / 44100) * 0.5;
    }
    mockAudioBuffer.getChannelData.mockReturnValue(sampleData);

    const { result } = renderHook(() =>
      useWaveformProcessor("test-audio.mp3", 30, 300, {
        // Start from frame 30 (1 second at 30fps)
        numPoints: 50,
        fps: 30,
      })
    );

    await waitFor(() => {
      expect(result.current).not.toBeNull();
    });

    expect(result.current?.peaks).toHaveLength(50);
  });

  it("should handle processing errors gracefully", async () => {
    // Mock fetch to fail
    (global.fetch as jest.Mock).mockRejectedValue(new Error("Network error"));

    const { result } = renderHook(() =>
      useWaveformProcessor("invalid-audio.mp3", 0, 300)
    );

    // Should not crash and should return null
    await waitFor(() => {
      expect(result.current).toBeNull();
    });
  });

  it("should use default options when none provided", async () => {
    const sampleData = new Float32Array(44100);
    for (let i = 0; i < sampleData.length; i++) {
      sampleData[i] = Math.sin((2 * Math.PI * 440 * i) / 44100) * 0.5;
    }
    mockAudioBuffer.getChannelData.mockReturnValue(sampleData);

    const { result } = renderHook(
      () => useWaveformProcessor("test-audio.mp3", 0, 300) // No options provided
    );

    await waitFor(() => {
      expect(result.current).not.toBeNull();
    });

    // Should use default numPoints (400)
    expect(result.current?.peaks).toHaveLength(400);
  });

  it("should handle audio shorter than video duration", async () => {
    // Create 10 seconds of audio data (44100 samples per second)
    const audioSamples = 44100 * 10; // 10 seconds
    const sampleData = new Float32Array(audioSamples);

    // Fill with audio data
    for (let i = 0; i < audioSamples; i++) {
      sampleData[i] = Math.sin((2 * Math.PI * 440 * i) / 44100) * 0.5;
    }

    mockAudioBuffer.getChannelData.mockReturnValue(sampleData);
    mockAudioBuffer.duration = 10; // 10 seconds of audio

    const { result } = renderHook(() =>
      useWaveformProcessor("test-audio.mp3", 0, 510, { // 17 seconds at 30fps
        numPoints: 100,
        fps: 30,
        silenceThreshold: 0.01,
      })
    );

    await waitFor(() => {
      expect(result.current).not.toBeNull();
    });

    const peaks = result.current?.peaks || [];

    // Calculate approximately where audio ends (10 seconds out of 17 seconds)
    const audioEndRatio = 10 / 17; // ~0.588
    const audioEndIndex = Math.floor(peaks.length * audioEndRatio);

    // First part should have audio data (some non-zero values)
    const audioPart = peaks.slice(0, audioEndIndex);
    const audioValues = audioPart.filter(peak => peak > 0);
    expect(audioValues.length).toBeGreaterThan(0);

    // Last part should be all zeros (no audio data)
    const silentPart = peaks.slice(audioEndIndex + 10); // Add buffer for edge cases
    const silentValues = silentPart.filter(peak => peak === 0);
    expect(silentValues.length).toBe(silentPart.length); // All should be zero
  });
});
