import React, {
  useCallback,
  useMemo,
  memo,
  useRef,
  useState,
  useEffect,
} from "react";
import { CaptionOverlay, Overlay, OverlayType } from "../../types";
import WaveformVisualizer from "../overlays/sounds/waveform-visualizer";
import { TimelineKeyframes } from "./timeline-keyframes";
import { useSidebar } from "../../contexts/sidebar-context";
import { TimelineItemHandle } from "./timeline-item-handle";
import { TimelineItemContextMenu } from "./timeline-item-context-menu";
import { TimelineItemLabel } from "./timeline-item-label";
import TimelineCaptionBlocks from "./timeline-caption-blocks";
import { useKeyframeContext } from "../../contexts/keyframe-context";
import { useAppContext } from "../../hooks/useAppContext";
import { useEditorContext } from "../../contexts/editor-context";
import Image from "next/image";
import { useDataVideoStore } from "@/store/dataVideo";
import { showPopupSetting } from "@/store/showPopupSetting";

/**
 * TimelineItem Component
 *
 * A draggable, resizable item displayed on the video editor timeline. Each item represents
 * a clip, text overlay, or sound element in the video composition.
 *
 * Features:
 * - Draggable positioning
 * - Resizable handles on both ends
 * - Context menu for quick actions
 * - Touch support for mobile devices
 * - Visual feedback for selection and dragging states
 * - Color-coded by content type (text, clip, sound)
 *
 * @component
 */

// Add new interface for waveform data
interface WaveformData {
  peaks: number[];
  length: number;
}

export interface TimelineItemProps {
  /** The overlay item data to be rendered */
  item: Overlay;
  /** Whether any item is currently being dragged */
  isDragging: boolean;
  /** Reference to the item currently being dragged, if any */
  draggedItem: Overlay | null;
  /** Currently selected item in the timeline */
  selectedItem: { id: number } | null;
  /** Callback to update the selected item */
  setSelectedItem: (item: { id: number }) => void;
  /** Handler for mouse-based drag and resize operations */
  handleMouseDown: (
    action: "move" | "resize-start" | "resize-end",
    e: React.MouseEvent<HTMLDivElement>
  ) => void;
  /** Handler for touch-based drag and resize operations */
  handleTouchStart: (
    action: "move" | "resize-start" | "resize-end",
    e: React.TouchEvent<HTMLDivElement>
  ) => void;
  /** Total duration of the timeline in frames */
  totalDuration: number;
  /** Callback to delete an item */
  onDeleteItem: (id: number) => void;
  /** Callback to duplicate an item */
  onDuplicateItem: (id: number) => void;
  /** Callback to split an item at the current position */
  onSplitItem: (id: number) => void;
  /** Callback fired when hovering over an item */
  onHover: (itemId: number, position: number) => void;
  /** Callback fired when context menu state changes */
  onContextMenuChange: (open: boolean) => void;
  /** Waveform data for audio items */
  waveformData?: WaveformData;
  /** Current Frame of the video */
  currentFrame?: number;
  /** Zoom scale of the timeline */
  zoomScale: number;
  /** Callback when asset loading state changes */
  onAssetLoadingChange?: (overlayId: number, isLoading: boolean) => void;
  /** Live push offset percentage during drag */
  livePushOffsetPercent?: number;
  isPlaying?: boolean;
  /** Active cut point state for visual feedback */
  activeCutPoint?: { overlayId: number | null; edge: "start" | "end" | null };
  /** Callback to select a cut point for adjustment */
  onCutPointSelect?: (overlayId: number, edge: "start" | "end") => void;
}

/** Height of each timeline item in pixels */
export const TIMELINE_ITEM_HEIGHT = 40;

const TimelineItem: React.FC<TimelineItemProps> = ({
  item,
  isDragging,
  draggedItem,
  selectedItem,
  setSelectedItem,
  handleMouseDown,
  handleTouchStart,
  totalDuration,
  onDeleteItem,
  onDuplicateItem,
  onSplitItem,
  onHover,
  onContextMenuChange,
  currentFrame,
  zoomScale,
  onAssetLoadingChange,
  livePushOffsetPercent = 0,
  isPlaying = false,
  activeCutPoint,
  onCutPointSelect,
}) => {
  // WaveformVisualizer now handles audio processing directly with WaveSurfer

  const isSelected = selectedItem?.id === item.id;
  const itemRef = useRef<HTMLDivElement>(null);
  const { setActivePanel, setIsOpen } = useSidebar();
  const keyframeContext = useKeyframeContext();
  const {
    setIsAnalyseVideoDone,
    setIsLoading,
    isLoading: isGenerating,
  } = useAppContext();
  const editorContext = useEditorContext();
  const { changeOverlay } = editorContext;
  const { setShowPopup, showPopup } = showPopupSetting();
  const setSegment = useDataVideoStore((state) => state.setSegment);
  const { setIsAnalyse, isAnalyse } = showPopupSetting();
  // New state variables for touch interactions
  const [touchStartTime, setTouchStartTime] = useState<number | null>(null);
  const [touchStartPosition, setTouchStartPosition] = useState<{
    x: number;
    y: number;
  } | null>(null);
  const [isTouching, setIsTouching] = useState(false);
  const touchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Constants for touch interactions
  const LONG_PRESS_DURATION = 500; // milliseconds
  const TOUCH_MOVEMENT_THRESHOLD = 10; // pixels

  /**
   * Handles mouse and touch interactions with the timeline item
   * Prevents event bubbling and triggers appropriate handlers based on the action
   */
  const handleItemInteraction = (
    e: React.MouseEvent | React.TouchEvent,
    action: "click" | "mousedown" | "touchstart"
  ) => {
    e.stopPropagation();

    // CRITICAL: Disable drag operations during playback
    if (isPlaying && (action === "mousedown" || action === "touchstart")) {
      return;
    }

    // Disable video overlay interactions during generation
    if (item.type === OverlayType.VIDEO && isGenerating) {
      return;
    }

    if (action === "click") {
      setSelectedItem({ id: item.id });
    } else if (action === "mousedown") {
      if (!isSelected) {
        setSelectedItem({ id: item.id });
      }
      handleMouseDown("move", e as React.MouseEvent<HTMLDivElement>);
    } else if (action === "touchstart") {
      const touchEvent = e as React.TouchEvent<HTMLDivElement>;
      const touch = touchEvent.touches[0];

      if (!isSelected) {
        setSelectedItem({ id: item.id });
      }

      setTouchStartTime(Date.now());
      setTouchStartPosition({ x: touch.clientX, y: touch.clientY });
      setIsTouching(true);

      if (touchTimeoutRef.current) {
        clearTimeout(touchTimeoutRef.current);
      }

      touchTimeoutRef.current = setTimeout(() => {
        if (isTouching) {
          setIsTouching(false);
        }
      }, LONG_PRESS_DURATION);
    }
  };

  // Handle touch move to distinguish between tap and drag
  const handleTouchMove = useCallback(
    (e: React.TouchEvent<HTMLDivElement>) => {
      if (isPlaying || !touchStartPosition) return; // Block during playback

      const touch = e.touches[0];
      const moveX = Math.abs(touch.clientX - touchStartPosition.x);
      const moveY = Math.abs(touch.clientY - touchStartPosition.y);

      if (
        moveX > TOUCH_MOVEMENT_THRESHOLD ||
        moveY > TOUCH_MOVEMENT_THRESHOLD
      ) {
        if (touchTimeoutRef.current) {
          clearTimeout(touchTimeoutRef.current);
          touchTimeoutRef.current = null;
        }

        handleTouchStart("move", e);

        setTouchStartTime(null);
        setTouchStartPosition(null);
        setIsTouching(false);
      }
    },
    [touchStartPosition, handleTouchStart, isPlaying] // Add isPlaying
  );

  // Handle touch end to detect taps
  const handleTouchEnd = useCallback(
    (e: React.TouchEvent<HTMLDivElement>) => {
      e.preventDefault();

      if (touchTimeoutRef.current) {
        clearTimeout(touchTimeoutRef.current);
        touchTimeoutRef.current = null;
      }

      // Allow selection even during playback, but disable sidebar opening
      if (touchStartTime && Date.now() - touchStartTime < LONG_PRESS_DURATION) {
        setSelectedItem({ id: item.id });

        // Only open sidebar when NOT playing
        if (
          !isPlaying &&
          (item.type === OverlayType.VIDEO ||
            item.type === OverlayType.TEXT ||
            item.type === OverlayType.SOUND ||
            item.type === OverlayType.CAPTION ||
            item.type === OverlayType.IMAGE)
        ) {
          setActivePanel(item.type);
          setIsOpen(true);
        }
      }

      setTouchStartTime(null);
      setTouchStartPosition(null);
      setIsTouching(false);
    },
    [
      touchStartTime,
      item.id,
      item.type,
      setSelectedItem,
      setActivePanel,
      setIsOpen,
      isPlaying, // Add isPlaying dependency
    ]
  );

  // Clean up timeout on unmount
  useEffect(() => {
    return () => {
      if (touchTimeoutRef.current) {
        clearTimeout(touchTimeoutRef.current);
      }
    };
  }, []);

  /**
   * Calculates and reports the hover position within the item
   * Used for showing precise position indicators while hovering
   */
  const handleMouseMove = useCallback(
    (e: React.MouseEvent<HTMLDivElement>) => {
      // CRITICAL: Don't handle mouse events during playback to prevent audio issues
      if (isPlaying) return;

      if (!e.currentTarget) {
        console.warn("Current target is null or undefined");
        return;
      }
      const rect = e.currentTarget.getBoundingClientRect();
      if (!rect) {
        console.warn("getBoundingClientRect returned null or undefined");
        return;
      }
      const relativeX = e.clientX - rect.left;
      const hoverPosition =
        item.from + (relativeX / rect.width) * item.durationInFrames;
      onHover(item.id, Math.round(hoverPosition));
    },
    [item, onHover, isPlaying] // Add isPlaying dependency
  );

  /**
   * Returns Tailwind CSS classes for styling based on content type
   */
  const getItemClasses = (
    type: OverlayType,
    isHandle: boolean = false,
    isSelected: boolean = false
  ): string => {
    switch (type) {
      case OverlayType.TEXT:
        return isHandle
          ? "bg-[#9E53E6] dark:bg-[#9E53E6]"
          : "bg-[#9E53E6] hover:bg-[#9E53E6] border-[#9E53E6] text-[#9E53E6]";
      case OverlayType.VIDEO:
        return isHandle
          ? "bg-white dark:bg-black"
          : isSelected
          ? "bg-white hover:bg-white text-white dark:text-black"
          : "bg-white hover:bg-white border-slate-900 dark:border-white text-white dark:text-black";
      case OverlayType.SOUND:
        return isHandle
          ? "bg-[#E49723] dark:bg-[#E49723]"
          : "bg-[#E49723] hover:bg-[#E49723] border-[#E49723] text-[#E49723]";
      case OverlayType.CAPTION:
        return isHandle
          ? "bg-blue-500"
          : "bg-blue-500/20 hover:bg-blue-500/30 border-blue-500 text-blue-700";
      case OverlayType.IMAGE:
        return isHandle
          ? "bg-emerald-500 dark:bg-emerald-500"
          : "bg-emerald-500 hover:bg-emerald-500 dark:bg-emerald-500 dark:hover:bg-emerald-500 border-emerald-500 dark:border-emerald-500 text-emerald-500 dark:text-white";
      case OverlayType.STICKER:
        return isHandle
          ? "bg-red-500 dark:bg-red-500"
          : "bg-red-500 hover:bg-red-500 dark:bg-red-500 dark:hover:bg-red-500 border-red-500 dark:border-red-500 text-red-500 dark:text-white";

      default:
        return isHandle
          ? "bg-gray-200 dark:bg-gray-700"
          : "bg-gray-100 hover:bg-gray-200 dark:bg-gray-600 dark:hover:bg-gray-500 border-gray-300 dark:border-gray-400 text-gray-950 dark:text-white";
    }
  };

  const itemClasses = useMemo(
    () => getItemClasses(item.type, false, isSelected),
    [item.type, isSelected]
  );

  // Function to update overlay name based on metadata
  const updateOverlayNameFromMetadata = async (aiVideoAnalytics: any) => {
    try {
      const timeStringToFrames = (
        timeString: string,
        fps: number = 30
      ): number => {
        if (timeString === "0") {
          return 0;
        }

        const [hours, minutes, secondsAndFrames] = timeString.split(":");
        const [seconds, frames = "0"] = secondsAndFrames.split(".");
        return (
          parseInt(hours) * 3600 * fps +
          parseInt(minutes) * 60 * fps +
          parseInt(seconds) * fps +
          parseInt(frames)
        );
      };

      let foundMatch = false;
      for (const [segmentKey, segmentData] of Object.entries(
        aiVideoAnalytics
      )) {
        const segment = segmentData as any;
        if (segment.startTime && segment.segmentName) {
          const startFrame = timeStringToFrames(segment.startTime);

          // Match based on whether the overlay frame falls within the segment's time range
          if (item.from >= startFrame) {
            // Update the overlay name if it's different
            const currentName = (item as any).name || `Segment ${item.id}`;
            if (currentName !== segment.segmentName) {
              changeOverlay(item.id, {
                name: segment.segmentName,
                segmentId: segment.segmentId, // simpan segmentId tanpa timpa overlayId
              } as any);
            }
            foundMatch = true;
            return;
          }
        }
      }
    } catch (error) {
      console.error("❌ Error updating overlay name from metadata:", error);
    }
  };

  const handleSelect = async (e: React.MouseEvent) => {
    e.stopPropagation();
    setShowPopup("ai");
    setIsAnalyseVideoDone(false);
    console.log("isAnalyse:", isAnalyse);
    // Disable video overlay selection during generation
    if (item.type === OverlayType.VIDEO && isGenerating) {
      return;
    }
    // Make sure we're setting the selection correctly
    setSelectedItem({ id: item.id });

    try {
      const hasElectronAPI =
        typeof window !== "undefined" && !!(window as any).electronAPI;

      // First try to get metadata from Electron storage
      let metadata = null;
      if (hasElectronAPI) {
        const hasMetadataMethod = !!(window as any).electronAPI
          ?.getLastSavedMetadata;

        if (hasMetadataMethod) {
          metadata = await (window as any).electronAPI.getLastSavedMetadata();
        }
      }

      // If no metadata from Electron, try to get it from project-loaded event data
      if (!metadata?.segments?.aiVideoAnalytics) {
        const projectLoadedEvent = new CustomEvent("request-project-data");
        window.dispatchEvent(projectLoadedEvent);

        // Try to access the project data that was loaded during the project opening process
        // This is a fallback when Electron metadata storage isn't available
        metadata = (window as any).lastLoadedProjectData;
      }

      if (metadata?.segments?.aiVideoAnalytics) {
        let matchingSegment = null;
        const timeStringToFrames = (
          timeString: string,
          fps: number = 30
        ): number => {
          if (timeString === "0") return 0;
          const [hours, minutes, secondsAndFrames] = timeString.split(":");
          const [seconds, frames = "0"] = secondsAndFrames.split(".");
          return (
            parseInt(hours) * 3600 * fps +
            parseInt(minutes) * 60 * fps +
            parseInt(seconds) * fps +
            parseInt(frames)
          );
        };

        // Search through all segments to find frame position match
        for (const [segmentKey, segmentData] of Object.entries(
          metadata.segments.aiVideoAnalytics
        )) {
          const segment = segmentData as any;
          if (segment.startTime) {
            const startFrame = timeStringToFrames(segment.startTime);

            if (item.from === startFrame) {
              matchingSegment = segment;
              break;
            }
          }
        }

        if (matchingSegment) {
          // Use the segmentId from the JSON data instead of the overlay ID
          const correctSegmentId = matchingSegment.segmentId || item.id;

          // setSegment({
          //   ...matchingSegment,
          //   id: correctSegmentId, // Ensure the segment data has the correct ID
          // });
          setIsAnalyseVideoDone(!!matchingSegment);
        } else {
          // Fallback to ID-based lookup
          const segmentKey = `segment${item.id}`;
          const hasAnalysis = metadata.segments.aiVideoAnalytics[segmentKey];

          // if (hasAnalysis) {
          //   const correctSegmentId = hasAnalysis.segmentId || item.id;
          //   setSegment({
          //     ...hasAnalysis,
          //     id: correctSegmentId,
          //   });
          // } else {
          //   setSegment(null);
          // }
          setIsAnalyseVideoDone(!!hasAnalysis);
        }

        // Update overlay name if we find a matching segment in metadata
        await updateOverlayNameFromMetadata(metadata.segments.aiVideoAnalytics);
      } else {
        // No aiVideoAnalytics data found, set to false
        setIsAnalyseVideoDone(false);
      }
    } catch (error) {
      // console.error("❌ Error checking segment analysis:", error);
      setIsAnalyseVideoDone(false);
    } finally {
      setIsLoading(false);
    }

    if (
      item.type === OverlayType.VIDEO ||
      item.type === OverlayType.TEXT ||
      item.type === OverlayType.SOUND ||
      item.type === OverlayType.CAPTION ||
      item.type === OverlayType.IMAGE
    ) {
      setActivePanel(item.type);
      setIsOpen(true);
    }
  };

  const renderContent = () => {
    return (
      <>
        {item.type === OverlayType.IMAGE ? (
          <div className="h-full w-full flex items-center">
            <Image
              src={item.src}
              alt=""
              draggable="false"
              onDragStart={(e) => e.preventDefault()}
              className="h-7 w-7 rounded-[1px] ml-6  object-cover"
            />
          </div>
        ) : (
          <div className="flex-1 flex items-center px-2">
            <TimelineItemLabel item={item} isSelected={isSelected} />
          </div>
        )}
        {item.type === OverlayType.CAPTION && (
          <div className="relative h-full">
            <TimelineCaptionBlocks
              captions={(item as CaptionOverlay).captions}
              durationInFrames={item.durationInFrames}
              currentFrame={currentFrame ?? 0}
              startFrame={item.from}
              totalDuration={totalDuration}
            />
          </div>
        )}
        {item.type === OverlayType.SOUND && item.src && (
          <div className="absolute inset-0">
            <WaveformVisualizer
              audioUrl={item.src}
              totalDuration={totalDuration}
              durationInFrames={item.durationInFrames}
              startFromSound={item.startFromSound}
            />
          </div>
        )}
        {item.type === OverlayType.VIDEO && (
          <TimelineKeyframes
            overlay={item}
            currentFrame={currentFrame ?? 0}
            zoomScale={zoomScale}
            onLoadingChange={(isLoading) =>
              onAssetLoadingChange?.(item.id, isLoading)
            }
            isSelected={isSelected}
          />
        )}
      </>
    );
  };

  // Helper function to check if a handle is the active cut point
  const isActiveCutPoint = (edge: "start" | "end") => {
    return (
      activeCutPoint?.overlayId === item.id && activeCutPoint?.edge === edge
    );
  };

  // Helper function to handle cut point selection
  const handleCutPointSelect = (edge: "start" | "end") => {
    onCutPointSelect?.(item.id, edge);
  };

  return (
    <TimelineItemContextMenu
      onOpenChange={onContextMenuChange}
      onDeleteItem={(itemId) => {
        try {
          if (item.type === OverlayType.VIDEO) {
            keyframeContext.clearKeyframes(String(itemId));
          }
          if (item.type === OverlayType.SOUND) {
            // Clean up audio-specific resources when deleting sound overlays
            if (item.src && item.src.startsWith("blob:")) {
              try {
                URL.revokeObjectURL(item.src);
              } catch (error) {
                console.warn("Failed to revoke object URL:", error);
              }
            }
          }
          // Use setTimeout to ensure state updates are not conflicting
          setTimeout(() => {
            onDeleteItem(itemId);
          }, 0);
        } catch (error) {
          console.error("Error during overlay deletion:", error);
          // Fallback: still try to delete the item
          onDeleteItem(itemId);
        }
      }}
      onDuplicateItem={onDuplicateItem}
      itemId={item.id}
    >
      <div
        ref={itemRef}
        className={`absolute inset-y-[0.9px] rounded-md shadow-md group 
        ${itemClasses} 
        ${isDragging && draggedItem?.id === item.id ? "opacity-50" : ""} 
        ${isTouching ? "scale-[0.98] opacity-80" : ""} 
        ${isSelected ? "border-2 border-orange-500" : "border-[0px]"} 
        select-none pointer-events-auto overflow-hidden
        ${isPlaying ? "cursor-default" : "cursor-grab"}
        ${
          item.type === OverlayType.VIDEO && isGenerating
            ? "opacity-60 cursor-not-allowed"
            : ""
        }`}
        style={{
          left: `${(item.from / totalDuration) * 100}%`,
          width: `${(item.durationInFrames / totalDuration) * 100}%`,
          zIndex: isDragging ? 1 : isSelected ? 35 : 30,
          transition: `opacity 0.2s ${
            livePushOffsetPercent !== 0
              ? ", transform 0s"
              : ", transform 0.2s ease-out"
          }`,
          transform: `translateX(${livePushOffsetPercent}%)`,
        }}
        onMouseDown={(e) => {
          e.stopPropagation();
          handleItemInteraction(e, "mousedown");
        }}
        onTouchStart={(e) => {
          e.stopPropagation();
          handleItemInteraction(e, "touchstart");
        }}
        onTouchMove={handleTouchMove}
        onTouchEnd={(e) => {
          e.stopPropagation();
          handleTouchEnd(e);
        }}
        onClick={(e) => {
          e.stopPropagation();
          handleSelect(e);
        }}
        {...(!isPlaying && { onMouseMove: handleMouseMove })}
      >
        {renderContent()}

        {/* LEFT HANDLE - Updated with cut point support */}
        <div
          className={`
            ${
              isActiveCutPoint("start")
                ? "ring-2 ring-blue-400 ring-offset-1 rounded-md"
                : ""
            }
            transition-all duration-150
          `}
          onClick={(e) => {
            e.stopPropagation();
            if (isSelected) {
              handleCutPointSelect("start");
            }
          }}
        >
          <TimelineItemHandle
            position="left"
            isSelected={isSelected}
            onMouseDown={(e) => {
              e.stopPropagation();
              if (isPlaying) return;

              // Disable video overlay resizing during generation
              if (item.type === OverlayType.VIDEO && isGenerating) {
                return;
              }

              if (!isSelected) {
                setSelectedItem({ id: item.id });
              }
              handleCutPointSelect("start");
              handleMouseDown("resize-start", e);
            }}
            onTouchStart={(e) => {
              e.stopPropagation();
              if (isPlaying) return;

              // Disable video overlay resizing during generation
              if (item.type === OverlayType.VIDEO && isGenerating) {
                return;
              }

              if (!isSelected) {
                setSelectedItem({ id: item.id });
              }
              handleCutPointSelect("start");
              handleTouchStart("resize-start", e);
            }}
          />
        </div>

        {/* RIGHT HANDLE - Updated with cut point support */}
        <div
          className={`
            ${
              isActiveCutPoint("end")
                ? "ring-2 ring-blue-400 ring-offset-1 rounded-md"
                : ""
            }
            transition-all duration-150
          `}
          onClick={(e) => {
            e.stopPropagation();
            if (isSelected) {
              handleCutPointSelect("end");
            }
          }}
        >
          <TimelineItemHandle
            position="right"
            isSelected={isSelected}
            onMouseDown={(e) => {
              e.stopPropagation();
              if (isPlaying) return;

              // Disable video overlay resizing during generation
              if (item.type === OverlayType.VIDEO && isGenerating) {
                return;
              }

              if (!isSelected) {
                setSelectedItem({ id: item.id });
              }
              handleCutPointSelect("end");
              handleMouseDown("resize-end", e);
            }}
            onTouchStart={(e) => {
              e.stopPropagation();
              if (isPlaying) return;

              // Disable video overlay resizing during generation
              if (item.type === OverlayType.VIDEO && isGenerating) {
                return;
              }

              if (!isSelected) {
                setSelectedItem({ id: item.id });
              }
              handleCutPointSelect("end");
              handleTouchStart("resize-end", e);
            }}
          />
        </div>
      </div>
    </TimelineItemContextMenu>
  );
};

export default memo(TimelineItem);
