import { memo, useRef, useEffect, useState, useMemo } from "react";
import { useWavesurfer } from "@wavesurfer/react";

/**
 * Interface for the WaveformVisualizer component props
 */
interface WaveformVisualizerProps {
  audioUrl: string;
  totalDuration: number;
  durationInFrames: number;
  startFromSound?: number;
  silenceThreshold?: number; // Threshold below which audio is considered silence (0-1)
}

const WaveformVisualizer = memo(
  ({
    audioUrl,
    totalDuration,
    durationInFrames,
    startFromSound = 0,
    silenceThreshold = 0.01,
  }: WaveformVisualizerProps) => {
    const containerRef = useRef<HTMLDivElement>(null);
    const [isReady, setIsReady] = useState(false);

    const { wavesurfer } = useWavesurfer({
      container: containerRef,
      height: 40,
      waveColor: "#ffffff",
      progressColor: "#ffffff80",
      cursorColor: "transparent",
      barWidth: 2,
      barGap: 1,
      barRadius: 0,
      normalize: true,
      fillParent: true,
      interact: false,
      backend: "WebAudio",
      // Add silence threshold configuration
      minPxPerSec: 50, // Minimum pixels per second for better resolution
      hideScrollbar: true,
    });

    useEffect(() => {
      if (!wavesurfer || !audioUrl) return;

      const loadAudio = async () => {
        try {
          await wavesurfer.load(audioUrl);
          setIsReady(true);
        } catch (error) {
          console.error("Error loading audio for waveform:", error);
          setIsReady(false);
        }
      };

      loadAudio();
    }, [wavesurfer, audioUrl]);

    return (
      <div className="w-full h-full relative">
        {!isReady && (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-xs text-gray-400">Loading waveform...</div>
          </div>
        )}
        <div
          ref={containerRef}
          className="w-full h-full"
          style={{
            opacity: isReady ? 1 : 0,
            transition: "opacity 0.3s ease-in-out",
          }}
        />
      </div>
    );
  }
);

WaveformVisualizer.displayName = "WaveformVisualizer";

export default WaveformVisualizer;
