import { memo, useMemo } from "react";
import { useWaveformProcessor } from "../../../hooks/use-waveform-processor";

/**
 * Interface for the WaveformVisualizer component props
 */
interface WaveformVisualizerProps {
  audioUrl: string;
  totalDuration: number;
  durationInFrames: number;
  startFromSound?: number;
  silenceThreshold?: number; // Threshold below which audio is considered silence (0-1)
}

const WaveformVisualizer = memo(
  ({
    audioUrl,
    totalDuration,
    durationInFrames,
    startFromSound = 0,
    silenceThreshold = 0.01,
  }: WaveformVisualizerProps) => {
    // Use our custom waveform processor with silence detection
    const waveformData = useWaveformProcessor(
      audioUrl,
      startFromSound,
      durationInFrames,
      {
        numPoints: 200, // More points for better resolution
        fps: 30,
        silenceThreshold,
      }
    );

    // Process waveform data for rendering
    const processedBars = useMemo(() => {
      if (!waveformData?.peaks) return [];

      return waveformData.peaks.map((peak, index) => {
        // Normalize peak (0-1) and apply visual curve
        const normalized = Math.min(peak, 1);
        const curved = Math.pow(normalized, 0.6); // Slight curve for better visual distribution
        const height = Math.max(curved * 100, peak > 0 ? 2 : 0); // Minimum 2% height for non-silent parts

        return {
          height,
          opacity: peak > 0 ? 1 : 0, // Completely hide silent sections
        };
      });
    }, [waveformData?.peaks]);

    const isReady = !!waveformData;

    return (
      <div className="absolute inset-0">
        <div
          className="w-full h-full flex items-end justify-center px-1"
          style={{
            opacity: isReady ? 1 : 0.3,
            transition: "opacity 0.3s ease",
          }}
        >
          {processedBars.map((bar, index) => (
            <div
              key={index}
              className="bg-white flex-1 mx-px rounded-t-sm"
              style={{
                height: `${bar.height}%`,
                opacity: bar.opacity,
                minHeight: bar.height > 0 ? "2px" : "0px",
              }}
            />
          ))}
        </div>
        {!isReady && (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-xs text-white opacity-50">Loading...</div>
          </div>
        )}
      </div>
    );
  }
);

WaveformVisualizer.displayName = "WaveformVisualizer";

export default WaveformVisualizer;
