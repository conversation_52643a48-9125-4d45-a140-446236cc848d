import { memo, useRef, useEffect, useState, useMemo } from "react";
import { useWavesurfer } from "@wavesurfer/react";

/**
 * Interface for the WaveformVisualizer component props
 */
interface WaveformVisualizerProps {
  audioUrl: string;
  totalDuration: number;
  durationInFrames: number;
  startFromSound?: number;
  silenceThreshold?: number; // Threshold below which audio is considered silence (0-1)
}

const WaveformVisualizer = memo(
  ({
    audioUrl,
    totalDuration,
    durationInFrames,
    startFromSound = 0,
    silenceThreshold = 0.01,
  }: WaveformVisualizerProps) => {
    const containerRef = useRef<HTMLDivElement>(null);
    const [isReady, setIsReady] = useState(false);

    const { wavesurfer } = useWavesurfer({
      container: containerRef,
      height: 40,
      waveColor: "#ffffff",
      progressColor: "#ffffff80",
      cursorColor: "transparent",
      barWidth: 3, // Increased from 2 to 3 for bolder bars
      barGap: 1,
      barRadius: 1, // Slight radius for smoother appearance
      normalize: true,
      fillParent: true,
      interact: false,
      backend: "WebAudio",
      minPxPerSec: 50,
      hideScrollbar: true,
      // Center the waveform vertically
      splitChannels: false,
      renderFunction: (channels, ctx) => {
        const { width, height } = ctx.canvas;
        ctx.clearRect(0, 0, width, height);

        const channel = channels[0];
        const barWidth = 3;
        const barGap = 1;
        const barCount = Math.floor(width / (barWidth + barGap));
        const samplesPerBar = Math.floor(channel.length / barCount);

        ctx.fillStyle = "#ffffff";

        for (let i = 0; i < barCount; i++) {
          const start = i * samplesPerBar;
          const end = Math.min(start + samplesPerBar, channel.length);

          // Calculate RMS for this bar
          let sum = 0;
          for (let j = start; j < end; j++) {
            sum += channel[j] * channel[j];
          }
          const rms = Math.sqrt(sum / (end - start));

          // Convert to bar height (centered)
          const barHeight = Math.min(rms * height * 2, height * 0.9); // Max 90% of height
          const x = i * (barWidth + barGap);
          const y = (height - barHeight) / 2; // Center vertically

          // Draw rounded rectangle
          ctx.beginPath();
          ctx.roundRect(x, y, barWidth, barHeight, 1);
          ctx.fill();
        }
      },
    });

    useEffect(() => {
      if (!wavesurfer || !audioUrl) return;

      const loadAudio = async () => {
        try {
          await wavesurfer.load(audioUrl);
          setIsReady(true);
        } catch (error) {
          console.error("Error loading audio for waveform:", error);
          setIsReady(false);
        }
      };

      loadAudio();
    }, [wavesurfer, audioUrl]);

    return (
      <div className="w-full h-full relative">
        {!isReady && (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-xs text-gray-400">Loading waveform...</div>
          </div>
        )}
        <div
          ref={containerRef}
          className="w-full h-full"
          style={{
            opacity: isReady ? 1 : 0,
            transition: "opacity 0.3s ease-in-out",
          }}
        />
      </div>
    );
  }
);

WaveformVisualizer.displayName = "WaveformVisualizer";

export default WaveformVisualizer;
