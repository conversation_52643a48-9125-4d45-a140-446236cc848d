import { memo, useRef, useEffect, useState } from "react";
import { useWavesurfer } from "@wavesurfer/react";

/**
 * Interface for the WaveformVisualizer component props
 */
interface WaveformVisualizerProps {
  audioUrl: string;
  totalDuration: number;
  durationInFrames: number;
  startFromSound?: number;
}

const WaveformVisualizer = memo(
  ({
    audioUrl,
    totalDuration,
    durationInFrames,
    startFromSound = 0,
  }: WaveformVisualizerProps) => {
    const containerRef = useRef<HTMLDivElement>(null);
    const [isReady, setIsReady] = useState(false);

    const { wavesurfer } = useWavesurfer({
      container: containerRef,
      height: 40,
      waveColor: "#ffffff",
      progressColor: "#ffffff80",
      cursorColor: "transparent",
      barWidth: 2,
      barGap: 1,
      barRadius: 0,
      normalize: true,
      fillParent: true,
      interact: false,
      backend: 'WebAudio',
    });

    useEffect(() => {
      if (!wavesurfer || !audioUrl) return;

      const loadAudio = async () => {
        try {
          await wavesurfer.load(audioUrl);
          setIsReady(true);
        } catch (error) {
          console.error('Error loading audio for waveform:', error);
          setIsReady(false);
        }
      };

      loadAudio();
    }, [wavesurfer, audioUrl]);

    return (
      <div className="absolute inset-0">
        <div
          ref={containerRef}
          className="w-full h-full"
          style={{
            opacity: isReady ? 1 : 0.3,
            transition: 'opacity 0.3s ease',
          }}
        />
        {!isReady && (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-xs text-white opacity-50">Loading...</div>
          </div>
        )}
      </div>
    );
  }
);

WaveformVisualizer.displayName = "WaveformVisualizer";

export default WaveformVisualizer;
